0000000000000000000000000000000000000000 5314190432f740a97dfddb74d077ff3c2b9ef20c Sov3rain <<EMAIL>> 1758117636 +0200	branch: Created from HEAD
5314190432f740a97dfddb74d077ff3c2b9ef20c c8f33d99e116351a20782078aa8b1314c74fa7d1 Sov3rain <<EMAIL>> 1758117647 +0200	commit: chore: add unit tests for `ExtensionFilter` and `StandaloneFileBrowser`, include README for test suite and test assembly definition
c8f33d99e116351a20782078aa8b1314c74fa7d1 d2b7eecc3c0642be8a6e2fe1a5e6d84f4437adaf Sov3rain <<EMAIL>> 1758118334 +0200	commit: chore: make `ExtensionFilter` fields readonly, handle null extensions with `Array.Empty<string>()`
d2b7eecc3c0642be8a6e2fe1a5e6d84f4437adaf 4218b80b034a4bf5ddd981038ce0bb4a2f0f08c9 Sov3rain <<EMAIL>> 1758292967 +0200	commit: chore: refactor `FileInputField` implementation, add prefab and menu-based creation support, move Editor scripts out of Runtime folder, and update dependencies
4218b80b034a4bf5ddd981038ce0bb4a2f0f08c9 3e6c42102938959034fb9d81e2ef5bab80efc2df Sov3rain <<EMAIL>> 1758294039 +0200	commit: chore: update demo scene configuration, adjust `EditorBuildSettings` for sample removal, and refactor `OpenFilePanel` to handle comma-separated extensions

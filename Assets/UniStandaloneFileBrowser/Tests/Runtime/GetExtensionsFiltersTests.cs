using System;
using System.Reflection;
using NUnit.Framework;
using USFB;

namespace USFB.Tests
{
    /// <summary>
    /// Comprehensive unit tests for the private static method GetExtensionsFilters
    /// in the StandaloneFileBrowser class. Tests various input scenarios and edge cases.
    /// </summary>
    [TestFixture]
    public class GetExtensionsFiltersTests
    {
        private MethodInfo _getExtensionsFiltersMethod;

        [SetUp]
        public void SetUp()
        {
            // Use reflection to access the private static method
            _getExtensionsFiltersMethod = typeof(StandaloneFileBrowser)
                .GetMethod("GetExtensionsFilters", BindingFlags.NonPublic | BindingFlags.Static);
            
            Assert.IsNotNull(_getExtensionsFiltersMethod, "GetExtensionsFilters method should be found via reflection");
        }

        #region Null and Empty Input Tests

        /// <summary>
        /// Tests that passing null returns null.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithNullInput_ReturnsNull()
        {
            // Arrange
            string input = null;

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNull(result, "GetExtensionsFilters should return null when input is null");
        }

        /// <summary>
        /// Tests that passing an empty string returns null.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithEmptyString_ReturnsNull()
        {
            // Arrange
            const string input = "";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNull(result, "GetExtensionsFilters should return null when input is empty string");
        }

        /// <summary>
        /// Tests that passing whitespace-only strings return null.
        /// </summary>
        [Test]
        [TestCase(" ")]
        [TestCase("  ")]
        [TestCase("\t")]
        [TestCase("\n")]
        [TestCase("\r\n")]
        [TestCase(" \t \n ")]
        public void GetExtensionsFilters_WithWhitespaceOnlyInput_ReturnsNull(string input)
        {
            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNull(result, $"GetExtensionsFilters should return null when input is whitespace-only: '{input}'");
        }

        #endregion

        #region Single Extension Tests

        /// <summary>
        /// Tests with a single file extension.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithSingleExtension_ReturnsCorrectFilter()
        {
            // Arrange
            const string input = "txt";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(1, result.Length, "Should return exactly one ExtensionFilter");
            Assert.AreEqual("", result[0].Name, "Filter name should be empty string");
            Assert.IsNotNull(result[0].Extensions, "Extensions array should not be null");
            Assert.AreEqual(1, result[0].Extensions.Length, "Extensions array should contain one element");
            Assert.AreEqual("txt", result[0].Extensions[0], "Extension should match input");
        }

        /// <summary>
        /// Tests with a single extension that has leading/trailing whitespace.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithSingleExtensionAndWhitespace_TrimsWhitespace()
        {
            // Arrange
            const string input = " txt ";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(1, result.Length, "Should return exactly one ExtensionFilter");
            Assert.AreEqual("", result[0].Name, "Filter name should be empty string");
            Assert.AreEqual("txt", result[0].Extensions[0], "Extension should be trimmed");
        }

        #endregion

        #region Multiple Extensions Tests

        /// <summary>
        /// Tests with comma-separated extensions.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithMultipleExtensions_ReturnsCorrectFilters()
        {
            // Arrange
            const string input = "txt,pdf,doc";
            var expectedExtensions = new[] { "txt", "pdf", "doc" };

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");

            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual("", result[i].Name, $"Filter {i} name should be empty string");
                Assert.IsNotNull(result[i].Extensions, $"Filter {i} extensions array should not be null");
                Assert.AreEqual(1, result[i].Extensions.Length, $"Filter {i} should contain one extension");
                Assert.AreEqual(expectedExtensions[i], result[i].Extensions[0], $"Filter {i} extension should match expected value");
            }
        }

        /// <summary>
        /// Tests with extensions that have leading/trailing whitespace.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithExtensionsAndWhitespace_TrimsWhitespace()
        {
            // Arrange
            const string input = " txt , pdf , doc ";
            var expectedExtensions = new[] { "txt", "pdf", "doc" };

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");

            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual(expectedExtensions[i], result[i].Extensions[0], 
                    $"Filter {i} extension should be trimmed to '{expectedExtensions[i]}'");
            }
        }

        #endregion

        #region Extensions with Dots Tests

        /// <summary>
        /// Tests behavior with extensions that include dots.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithExtensionsIncludingDots_PreservesDots()
        {
            // Arrange
            const string input = ".txt,.pdf";
            var expectedExtensions = new[] { ".txt", ".pdf" };

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(2, result.Length, "Should return exactly two ExtensionFilters");

            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual(expectedExtensions[i], result[i].Extensions[0], 
                    $"Filter {i} extension should preserve dots: '{expectedExtensions[i]}'");
            }
        }

        /// <summary>
        /// Tests mixed extensions with and without dots.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithMixedDotExtensions_PreservesOriginalFormat()
        {
            // Arrange
            const string input = "txt,.pdf,doc,.docx";
            var expectedExtensions = new[] { "txt", ".pdf", "doc", ".docx" };

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(4, result.Length, "Should return exactly four ExtensionFilters");

            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual(expectedExtensions[i], result[i].Extensions[0], 
                    $"Filter {i} extension should preserve original format: '{expectedExtensions[i]}'");
            }
        }

        #endregion

        #region Empty Extensions in List Tests

        /// <summary>
        /// Tests behavior when there are empty values between commas.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithEmptyExtensionsInList_CreatesFiltersForEmptyStrings()
        {
            // Arrange
            const string input = "txt,,pdf";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");
            Assert.AreEqual("txt", result[0].Extensions[0], "First extension should be 'txt'");
            Assert.AreEqual("", result[1].Extensions[0], "Second extension should be empty string");
            Assert.AreEqual("pdf", result[2].Extensions[0], "Third extension should be 'pdf'");
        }

        /// <summary>
        /// Tests behavior with multiple consecutive empty values.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithMultipleEmptyExtensions_CreatesFiltersForAllEmptyStrings()
        {
            // Arrange
            const string input = "txt,,,pdf";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(4, result.Length, "Should return exactly four ExtensionFilters");
            Assert.AreEqual("txt", result[0].Extensions[0], "First extension should be 'txt'");
            Assert.AreEqual("", result[1].Extensions[0], "Second extension should be empty string");
            Assert.AreEqual("", result[2].Extensions[0], "Third extension should be empty string");
            Assert.AreEqual("pdf", result[3].Extensions[0], "Fourth extension should be 'pdf'");
        }

        /// <summary>
        /// Tests behavior with whitespace-only values between commas.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithWhitespaceOnlyExtensions_TrimsToEmptyStrings()
        {
            // Arrange
            const string input = "txt, ,pdf";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");
            Assert.AreEqual("txt", result[0].Extensions[0], "First extension should be 'txt'");
            Assert.AreEqual("", result[1].Extensions[0], "Second extension should be empty string after trimming");
            Assert.AreEqual("pdf", result[2].Extensions[0], "Third extension should be 'pdf'");
        }

        #endregion

        #region Edge Cases Tests

        /// <summary>
        /// Tests with only commas.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithOnlyCommas_CreatesEmptyExtensionFilters()
        {
            // Arrange
            const string input = ",,";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");
            
            for (int i = 0; i < result.Length; i++)
            {
                Assert.AreEqual("", result[i].Extensions[0], $"Filter {i} extension should be empty string");
            }
        }

        /// <summary>
        /// Tests with single comma.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithSingleComma_CreatesTwoEmptyExtensionFilters()
        {
            // Arrange
            const string input = ",";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(2, result.Length, "Should return exactly two ExtensionFilters");
            Assert.AreEqual("", result[0].Extensions[0], "First extension should be empty string");
            Assert.AreEqual("", result[1].Extensions[0], "Second extension should be empty string");
        }

        /// <summary>
        /// Tests with very long extension list.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithManyExtensions_HandlesLargeList()
        {
            // Arrange
            var extensions = new string[50];
            for (int i = 0; i < 50; i++)
            {
                extensions[i] = $"ext{i:D2}";
            }
            var input = string.Join(",", extensions);

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(50, result.Length, "Should return exactly fifty ExtensionFilters");
            
            for (int i = 0; i < 50; i++)
            {
                Assert.AreEqual($"ext{i:D2}", result[i].Extensions[0], $"Filter {i} should have correct extension");
            }
        }

        /// <summary>
        /// Tests with special characters in extensions.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithSpecialCharacters_PreservesSpecialCharacters()
        {
            // Arrange
            const string input = "txt-backup,file.old,temp~";
            var expectedExtensions = new[] { "txt-backup", "file.old", "temp~" };

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");

            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual(expectedExtensions[i], result[i].Extensions[0],
                    $"Filter {i} should preserve special characters: '{expectedExtensions[i]}'");
            }
        }

        /// <summary>
        /// Tests with numeric extensions.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithNumericExtensions_HandlesNumericValues()
        {
            // Arrange
            const string input = "001,123,999";
            var expectedExtensions = new[] { "001", "123", "999" };

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");

            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual(expectedExtensions[i], result[i].Extensions[0],
                    $"Filter {i} should handle numeric extension: '{expectedExtensions[i]}'");
            }
        }

        /// <summary>
        /// Tests that each ExtensionFilter has exactly one extension (not multiple).
        /// </summary>
        [Test]
        public void GetExtensionsFilters_EachFilterHasSingleExtension_VerifyFilterStructure()
        {
            // Arrange
            const string input = "txt,pdf,doc";

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");

            foreach (var filter in result)
            {
                Assert.AreEqual("", filter.Name, "Each filter should have empty name");
                Assert.IsNotNull(filter.Extensions, "Each filter should have non-null Extensions array");
                Assert.AreEqual(1, filter.Extensions.Length, "Each filter should have exactly one extension");
            }
        }

        #endregion

        #region Performance and Stress Tests

        /// <summary>
        /// Tests with extremely long single extension.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithVeryLongExtension_HandlesLongString()
        {
            // Arrange
            var longExtension = new string('a', 1000);
            var input = longExtension;

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(1, result.Length, "Should return exactly one ExtensionFilter");
            Assert.AreEqual(longExtension, result[0].Extensions[0], "Should handle very long extension");
        }

        /// <summary>
        /// Tests with Unicode characters in extensions.
        /// </summary>
        [Test]
        public void GetExtensionsFilters_WithUnicodeCharacters_PreservesUnicode()
        {
            // Arrange
            const string input = "文档,файл,αρχείο";
            var expectedExtensions = new[] { "文档", "файл", "αρχείο" };

            // Act
            var result = (ExtensionFilter[])_getExtensionsFiltersMethod.Invoke(null, new object[] { input });

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return exactly three ExtensionFilters");

            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual(expectedExtensions[i], result[i].Extensions[0],
                    $"Filter {i} should preserve Unicode characters: '{expectedExtensions[i]}'");
            }
        }

        #endregion
    }
}

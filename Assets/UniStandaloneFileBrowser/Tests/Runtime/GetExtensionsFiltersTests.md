# GetExtensionsFilters Method Test Documentation

## Overview

This document describes the comprehensive unit tests for the private static method `GetExtensionsFilters` in the `StandaloneFileBrowser` class. The tests ensure the method correctly handles various input scenarios and edge cases.

## Test Coverage

### 1. Null and Empty Input Tests
- **Null input**: Verifies that passing `null` returns `null`
- **Empty string**: Verifies that passing `""` returns `null`
- **Whitespace-only**: Tests various whitespace combinations (spaces, tabs, newlines) return `null`

### 2. Single Extension Tests
- **Basic single extension**: Tests with simple extension like "txt"
- **Extension with whitespace**: Verifies trimming of leading/trailing whitespace

### 3. Multiple Extensions Tests
- **Comma-separated extensions**: Tests "txt,pdf,doc" format
- **Extensions with whitespace**: Verifies trimming in comma-separated list

### 4. Extensions with Dots Tests
- **Extensions including dots**: Tests ".txt,.pdf" format
- **Mixed dot extensions**: Tests combination of "txt,.pdf,doc,.docx"

### 5. Empty Extensions in List Tests
- **Empty values between commas**: Tests "txt,,pdf" format
- **Multiple consecutive empty values**: Tests "txt,,,pdf" format
- **Whitespace-only values**: Tests "txt, ,pdf" format

### 6. Edge Cases Tests
- **Only commas**: Tests ",," input
- **Single comma**: Tests "," input
- **Large extension list**: Tests with 50 extensions
- **Special characters**: Tests extensions with hyphens, dots, tildes
- **Numeric extensions**: Tests "001,123,999" format
- **Filter structure verification**: Ensures each filter has exactly one extension

### 7. Performance and Stress Tests
- **Very long extension**: Tests with 1000-character extension
- **Unicode characters**: Tests with Chinese, Cyrillic, and Greek characters

## Test Implementation Details

### Reflection Usage
Since `GetExtensionsFilters` is a private static method, the tests use reflection to access it:

```csharp
_getExtensionsFiltersMethod = typeof(StandaloneFileBrowser)
    .GetMethod("GetExtensionsFilters", BindingFlags.NonPublic | BindingFlags.Static);
```

### Test Structure
Each test follows the Arrange-Act-Assert pattern:
- **Arrange**: Set up input parameters
- **Act**: Invoke the method via reflection
- **Assert**: Verify the results match expectations

### Assertions
For each test, the following aspects are verified:
- Return value is not null (when expected)
- Correct number of `ExtensionFilter` objects returned
- Each `ExtensionFilter` has empty string as name
- Each `ExtensionFilter` has exactly one extension
- Extension values match expected results
- Proper handling of whitespace trimming

## Running the Tests

### Unity Test Runner
1. Open Unity Editor
2. Go to `Window > General > Test Runner`
3. Select "PlayMode" tab
4. Look for `GetExtensionsFiltersTests` in the test list
5. Click "Run All" or select specific tests

### Command Line (if Unity is in PATH)
```bash
Unity.exe -batchmode -quit -projectPath "path/to/project" -runTests -testPlatform PlayMode -testFilter "GetExtensionsFiltersTests"
```

## Expected Behavior

Based on the method implementation:

1. **Null/Empty Input**: Returns `null` for null, empty, or whitespace-only strings
2. **String Splitting**: Splits input on commas using `String.Split(',')`
3. **Whitespace Handling**: Trims each extension using `String.Trim()`
4. **Filter Creation**: Creates one `ExtensionFilter` per extension with:
   - Empty string as the name
   - Single extension in the Extensions array
5. **Edge Cases**: Handles empty strings between commas by creating filters with empty extensions

## Test File Location

```
Assets/UniStandaloneFileBrowser/Tests/Runtime/GetExtensionsFiltersTests.cs
```

## Dependencies

- NUnit Framework (included in Unity Test Framework)
- System.Reflection (for accessing private method)
- USFB namespace (for StandaloneFileBrowser and ExtensionFilter)

## Notes

- The method has unreachable code at line 244 (`if (extensions.Length == 0) return null;`) because `String.Split(',')` never returns an empty array
- Tests are designed to work with the current implementation regardless of this unreachable code
- All tests use descriptive names following the pattern: `MethodName_Scenario_ExpectedResult`
- Tests include comprehensive error messages for easier debugging
